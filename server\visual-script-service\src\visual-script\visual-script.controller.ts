/**
 * visual-script.controller.ts
 * 
 * 视觉脚本控制器
 */

import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  HttpCode,
  ParseUUIDPipe,
  ValidationPipe
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { VisualScriptService } from './visual-script.service';
import { CreateScriptDto, UpdateScriptDto, ScriptQueryDto, ExecuteScriptDto, ShareScriptDto } from './dto/script.dto';
import { VisualScript } from './entities/visual-script.entity';

@ApiTags('视觉脚本')
@Controller('visual-scripts')
@ApiBearerAuth()
export class VisualScriptController {
  constructor(private readonly visualScriptService: VisualScriptService) {}

  @Post()
  @ApiOperation({ summary: '创建新脚本' })
  @ApiResponse({ status: 201, description: '脚本创建成功', type: VisualScript })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 401, description: '未授权' })
  async create(
    @Body(ValidationPipe) createScriptDto: CreateScriptDto,
    @Request() req: any
  ): Promise<VisualScript> {
    const { userId, userName } = req.user;
    return await this.visualScriptService.create(createScriptDto, userId, userName);
  }

  @Get()
  @ApiOperation({ summary: '获取脚本列表' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiQuery({ name: 'page', required: false, description: '页码' })
  @ApiQuery({ name: 'limit', required: false, description: '每页数量' })
  @ApiQuery({ name: 'search', required: false, description: '搜索关键词' })
  @ApiQuery({ name: 'status', required: false, description: '脚本状态' })
  @ApiQuery({ name: 'visibility', required: false, description: '可见性' })
  @ApiQuery({ name: 'ownerId', required: false, description: '所有者ID' })
  @ApiQuery({ name: 'projectId', required: false, description: '项目ID' })
  @ApiQuery({ name: 'tags', required: false, description: '标签（逗号分隔）' })
  @ApiQuery({ name: 'sortBy', required: false, description: '排序字段' })
  @ApiQuery({ name: 'sortOrder', required: false, description: '排序方向' })
  async findAll(
    @Query(ValidationPipe) query: ScriptQueryDto,
    @Request() req: any
  ): Promise<{ scripts: VisualScript[]; total: number; page: number; limit: number }> {
    const userId = req.user?.userId;
    const result = await this.visualScriptService.findMany(query, userId);
    
    return {
      ...result,
      page: query.page || 1,
      limit: query.limit || 20
    };
  }

  @Get(':id')
  @ApiOperation({ summary: '获取脚本详情' })
  @ApiResponse({ status: 200, description: '获取成功', type: VisualScript })
  @ApiResponse({ status: 404, description: '脚本不存在' })
  @ApiResponse({ status: 403, description: '没有访问权限' })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any
  ): Promise<VisualScript> {
    const userId = req.user?.userId;
    return await this.visualScriptService.findOne(id, userId);
  }

  @Put(':id')
  @ApiOperation({ summary: '更新脚本' })
  @ApiResponse({ status: 200, description: '更新成功', type: VisualScript })
  @ApiResponse({ status: 404, description: '脚本不存在' })
  @ApiResponse({ status: 403, description: '没有编辑权限' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateScriptDto: UpdateScriptDto,
    @Request() req: any
  ): Promise<VisualScript> {
    const { userId } = req.user;
    return await this.visualScriptService.update(id, updateScriptDto, userId);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: '删除脚本' })
  @ApiResponse({ status: 204, description: '删除成功' })
  @ApiResponse({ status: 404, description: '脚本不存在' })
  @ApiResponse({ status: 403, description: '没有删除权限' })
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any
  ): Promise<void> {
    const { userId } = req.user;
    await this.visualScriptService.remove(id, userId);
  }

  @Post(':id/publish')
  @ApiOperation({ summary: '发布脚本' })
  @ApiResponse({ status: 200, description: '发布成功', type: VisualScript })
  @ApiResponse({ status: 404, description: '脚本不存在' })
  @ApiResponse({ status: 403, description: '没有发布权限' })
  async publish(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any
  ): Promise<VisualScript> {
    const { userId } = req.user;
    return await this.visualScriptService.publish(id, userId);
  }

  @Post(':id/archive')
  @ApiOperation({ summary: '归档脚本' })
  @ApiResponse({ status: 200, description: '归档成功', type: VisualScript })
  @ApiResponse({ status: 404, description: '脚本不存在' })
  @ApiResponse({ status: 403, description: '没有归档权限' })
  async archive(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any
  ): Promise<VisualScript> {
    const { userId } = req.user;
    return await this.visualScriptService.archive(id, userId);
  }

  @Post(':id/duplicate')
  @ApiOperation({ summary: '复制脚本' })
  @ApiResponse({ status: 201, description: '复制成功', type: VisualScript })
  @ApiResponse({ status: 404, description: '脚本不存在' })
  @ApiResponse({ status: 403, description: '没有访问权限' })
  async duplicate(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any
  ): Promise<VisualScript> {
    const { userId, userName } = req.user;
    return await this.visualScriptService.duplicate(id, userId, userName);
  }

  @Post(':id/execute')
  @ApiOperation({ summary: '执行脚本' })
  @ApiResponse({ status: 200, description: '执行请求已提交' })
  @ApiResponse({ status: 404, description: '脚本不存在' })
  @ApiResponse({ status: 403, description: '没有执行权限' })
  async execute(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) executeDto: ExecuteScriptDto,
    @Request() req: any
  ): Promise<{ executionId: string; status: string }> {
    const { userId } = req.user;
    // TODO: 实现脚本执行逻辑
    return {
      executionId: 'temp-execution-id',
      status: 'pending'
    };
  }

  @Post(':id/share')
  @ApiOperation({ summary: '分享脚本' })
  @ApiResponse({ status: 200, description: '分享成功' })
  @ApiResponse({ status: 404, description: '脚本不存在' })
  @ApiResponse({ status: 403, description: '没有分享权限' })
  async share(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) shareDto: ShareScriptDto,
    @Request() req: any
  ): Promise<{ message: string; invitedUsers: number }> {
    const { userId } = req.user;
    // TODO: 实现脚本分享逻辑
    return {
      message: '分享邀请已发送',
      invitedUsers: shareDto.userIds.length
    };
  }

  @Get(':id/stats')
  @ApiOperation({ summary: '获取脚本统计信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 404, description: '脚本不存在' })
  @ApiResponse({ status: 403, description: '没有访问权限' })
  async getStats(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any
  ): Promise<any> {
    const userId = req.user?.userId;
    const script = await this.visualScriptService.findOne(id, userId);
    
    return {
      scriptId: script.id,
      executionCount: script.executionCount,
      nodeCount: script.nodeCount,
      connectionCount: script.connectionCount,
      fileSize: script.fileSize,
      lastExecutedAt: script.lastExecutedAt,
      createdAt: script.createdAt,
      updatedAt: script.updatedAt
    };
  }

  @Get(':id/export')
  @ApiOperation({ summary: '导出脚本' })
  @ApiResponse({ status: 200, description: '导出成功' })
  @ApiResponse({ status: 404, description: '脚本不存在' })
  @ApiResponse({ status: 403, description: '没有访问权限' })
  async export(
    @Param('id', ParseUUIDPipe) id: string,
    @Query('format') format: string = 'json',
    @Query('includeMetadata') includeMetadata: boolean = true,
    @Request() req: any
  ): Promise<any> {
    const userId = req.user?.userId;
    const script = await this.visualScriptService.findOne(id, userId);
    
    const exportData: any = {
      id: script.id,
      name: script.name,
      description: script.description,
      graph: script.graph,
      version: script.currentVersion,
      exportedAt: new Date().toISOString()
    };

    if (includeMetadata) {
      exportData.metadata = script.metadata;
      exportData.tags = script.tags;
      exportData.nodeCount = script.nodeCount;
      exportData.connectionCount = script.connectionCount;
    }

    return exportData;
  }

  @Get(':id/validate')
  @ApiOperation({ summary: '验证脚本' })
  @ApiResponse({ status: 200, description: '验证完成' })
  @ApiResponse({ status: 404, description: '脚本不存在' })
  @ApiResponse({ status: 403, description: '没有访问权限' })
  async validate(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any
  ): Promise<{ valid: boolean; errors: string[]; warnings: string[] }> {
    const userId = req.user?.userId;
    const script = await this.visualScriptService.findOne(id, userId);
    
    // TODO: 实现脚本验证逻辑
    return {
      valid: true,
      errors: [],
      warnings: []
    };
  }
}
