/**
 * ImageProcessingNodes.ts
 * 
 * 图像处理相关的视觉脚本节点
 */

import { FlowNode } from '../nodes/FlowNode';
import { NodeCategory } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { ExecutionContext } from '../execution/ExecutionContext';

/**
 * 加载图像节点
 */
export class LoadImageNode extends FlowNode {
  constructor() {
    super();
    
    // 输入插槽
    this.addInputSlot('trigger', 'flow', '触发');
    this.addInputSlot('src', 'string', '图像源', '');
    this.addInputSlot('crossOrigin', 'string', '跨域设置', 'anonymous');
    
    // 输出插槽
    this.addOutputSlot('success', 'flow', '成功');
    this.addOutputSlot('fail', 'flow', '失败');
    this.addOutputSlot('image', 'object', '图像对象');
    this.addOutputSlot('width', 'number', '图像宽度');
    this.addOutputSlot('height', 'number', '图像高度');
    this.addOutputSlot('error', 'string', '错误信息');
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const src = this.getInputValue('src') as string;
    const crossOrigin = this.getInputValue('crossOrigin') as string;

    try {
      // 创建图像对象
      const image = new Image();
      image.crossOrigin = crossOrigin;

      // 返回Promise以等待图像加载
      const loadPromise = new Promise<HTMLImageElement>((resolve, reject) => {
        image.onload = () => resolve(image);
        image.onerror = () => reject(new Error('图像加载失败'));
      });

      // 开始加载图像
      image.src = src;
      
      // 等待加载完成
      const loadedImage = await loadPromise;

      // 设置输出值
      this.setOutputValue('image', loadedImage);
      this.setOutputValue('width', loadedImage.width);
      this.setOutputValue('height', loadedImage.height);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('加载图像失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 调整图像大小节点
 */
export class ResizeImageNode extends FlowNode {
  constructor() {
    super();
    
    // 输入插槽
    this.addInputSlot('trigger', 'flow', '触发');
    this.addInputSlot('image', 'object', '源图像');
    this.addInputSlot('width', 'number', '目标宽度', 100);
    this.addInputSlot('height', 'number', '目标高度', 100);
    this.addInputSlot('quality', 'number', '质量', 0.9);
    this.addInputSlot('format', 'string', '输出格式', 'image/png');
    
    // 输出插槽
    this.addOutputSlot('success', 'flow', '成功');
    this.addOutputSlot('fail', 'flow', '失败');
    this.addOutputSlot('resizedImage', 'object', '调整后图像');
    this.addOutputSlot('dataURL', 'string', '数据URL');
    this.addOutputSlot('error', 'string', '错误信息');
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const image = this.getInputValue('image') as HTMLImageElement;
    const width = this.getInputValue('width') as number;
    const height = this.getInputValue('height') as number;
    const quality = this.getInputValue('quality') as number;
    const format = this.getInputValue('format') as string;

    try {
      if (!image) {
        throw new Error('源图像为空');
      }

      // 创建画布
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      if (!ctx) {
        throw new Error('无法创建画布上下文');
      }

      // 设置画布大小
      canvas.width = width;
      canvas.height = height;

      // 绘制调整大小的图像
      ctx.drawImage(image, 0, 0, width, height);

      // 获取数据URL
      const dataURL = canvas.toDataURL(format, quality);

      // 创建新的图像对象
      const resizedImage = new Image();
      resizedImage.src = dataURL;

      // 设置输出值
      this.setOutputValue('resizedImage', resizedImage);
      this.setOutputValue('dataURL', dataURL);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('调整图像大小失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 图像滤镜节点
 */
export class ImageFilterNode extends FlowNode {
  constructor() {
    super();
    
    // 输入插槽
    this.addInputSlot('trigger', 'flow', '触发');
    this.addInputSlot('image', 'object', '源图像');
    this.addInputSlot('filterType', 'string', '滤镜类型', 'blur');
    this.addInputSlot('intensity', 'number', '强度', 1.0);
    
    // 输出插槽
    this.addOutputSlot('success', 'flow', '成功');
    this.addOutputSlot('fail', 'flow', '失败');
    this.addOutputSlot('filteredImage', 'object', '滤镜后图像');
    this.addOutputSlot('dataURL', 'string', '数据URL');
    this.addOutputSlot('error', 'string', '错误信息');
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const image = this.getInputValue('image') as HTMLImageElement;
    const filterType = this.getInputValue('filterType') as string;
    const intensity = this.getInputValue('intensity') as number;

    try {
      if (!image) {
        throw new Error('源图像为空');
      }

      // 创建画布
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      if (!ctx) {
        throw new Error('无法创建画布上下文');
      }

      // 设置画布大小
      canvas.width = image.width;
      canvas.height = image.height;

      // 应用滤镜
      this.applyFilter(ctx, filterType, intensity);

      // 绘制图像
      ctx.drawImage(image, 0, 0);

      // 获取数据URL
      const dataURL = canvas.toDataURL();

      // 创建新的图像对象
      const filteredImage = new Image();
      filteredImage.src = dataURL;

      // 设置输出值
      this.setOutputValue('filteredImage', filteredImage);
      this.setOutputValue('dataURL', dataURL);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('应用图像滤镜失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      this.triggerFlow('fail');
      return false;
    }
  }

  private applyFilter(ctx: CanvasRenderingContext2D, filterType: string, intensity: number): void {
    switch (filterType) {
      case 'blur':
        ctx.filter = `blur(${intensity}px)`;
        break;
      case 'brightness':
        ctx.filter = `brightness(${intensity})`;
        break;
      case 'contrast':
        ctx.filter = `contrast(${intensity})`;
        break;
      case 'grayscale':
        ctx.filter = `grayscale(${intensity})`;
        break;
      case 'sepia':
        ctx.filter = `sepia(${intensity})`;
        break;
      case 'saturate':
        ctx.filter = `saturate(${intensity})`;
        break;
      case 'hue-rotate':
        ctx.filter = `hue-rotate(${intensity}deg)`;
        break;
      default:
        ctx.filter = 'none';
    }
  }
}

/**
 * 图像裁剪节点
 */
export class CropImageNode extends FlowNode {
  constructor() {
    super();
    
    // 输入插槽
    this.addInputSlot('trigger', 'flow', '触发');
    this.addInputSlot('image', 'object', '源图像');
    this.addInputSlot('x', 'number', 'X坐标', 0);
    this.addInputSlot('y', 'number', 'Y坐标', 0);
    this.addInputSlot('width', 'number', '裁剪宽度', 100);
    this.addInputSlot('height', 'number', '裁剪高度', 100);
    
    // 输出插槽
    this.addOutputSlot('success', 'flow', '成功');
    this.addOutputSlot('fail', 'flow', '失败');
    this.addOutputSlot('croppedImage', 'object', '裁剪后图像');
    this.addOutputSlot('dataURL', 'string', '数据URL');
    this.addOutputSlot('error', 'string', '错误信息');
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const image = this.getInputValue('image') as HTMLImageElement;
    const x = this.getInputValue('x') as number;
    const y = this.getInputValue('y') as number;
    const width = this.getInputValue('width') as number;
    const height = this.getInputValue('height') as number;

    try {
      if (!image) {
        throw new Error('源图像为空');
      }

      // 创建画布
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      if (!ctx) {
        throw new Error('无法创建画布上下文');
      }

      // 设置画布大小为裁剪区域大小
      canvas.width = width;
      canvas.height = height;

      // 绘制裁剪的图像部分
      ctx.drawImage(image, x, y, width, height, 0, 0, width, height);

      // 获取数据URL
      const dataURL = canvas.toDataURL();

      // 创建新的图像对象
      const croppedImage = new Image();
      croppedImage.src = dataURL;

      // 设置输出值
      this.setOutputValue('croppedImage', croppedImage);
      this.setOutputValue('dataURL', dataURL);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('裁剪图像失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 注册图像处理节点
 */
export function registerImageProcessingNodes(registry: NodeRegistry): void {
  // 注册加载图像节点
  registry.registerNodeType({
    type: 'image/load',
    category: NodeCategory.IMAGE,
    constructor: LoadImageNode,
    label: '加载图像',
    description: '从URL加载图像',
    icon: 'image',
    color: '#722ed1',
    tags: ['image', 'load', 'url', 'media']
  });

  // 注册调整图像大小节点
  registry.registerNodeType({
    type: 'image/resize',
    category: NodeCategory.IMAGE,
    constructor: ResizeImageNode,
    label: '调整图像大小',
    description: '调整图像的宽度和高度',
    icon: 'resize',
    color: '#722ed1',
    tags: ['image', 'resize', 'scale', 'transform']
  });

  // 注册图像滤镜节点
  registry.registerNodeType({
    type: 'image/filter',
    category: NodeCategory.IMAGE,
    constructor: ImageFilterNode,
    label: '图像滤镜',
    description: '对图像应用各种滤镜效果',
    icon: 'filter',
    color: '#722ed1',
    tags: ['image', 'filter', 'effect', 'processing']
  });

  // 注册图像裁剪节点
  registry.registerNodeType({
    type: 'image/crop',
    category: NodeCategory.IMAGE,
    constructor: CropImageNode,
    label: '裁剪图像',
    description: '裁剪图像的指定区域',
    icon: 'crop',
    color: '#722ed1',
    tags: ['image', 'crop', 'cut', 'region']
  });
}
