/**
 * UINodes.ts
 * 
 * UI相关的视觉脚本节点
 */

import { FlowNode } from '../nodes/FlowNode';
import { NodeCategory } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { ExecutionContext } from '../execution/ExecutionContext';

/**
 * 创建按钮节点
 */
export class CreateButtonNode extends FlowNode {
  constructor() {
    super();
    
    // 输入插槽
    this.addInputSlot('trigger', 'flow', '触发');
    this.addInputSlot('text', 'string', '按钮文本', '按钮');
    this.addInputSlot('position', 'vector2', '位置', { x: 0, y: 0 });
    this.addInputSlot('size', 'vector2', '大小', { x: 100, y: 40 });
    this.addInputSlot('style', 'object', '样式', {});
    
    // 输出插槽
    this.addOutputSlot('success', 'flow', '成功');
    this.addOutputSlot('onClick', 'flow', '点击事件');
    this.addOutputSlot('button', 'object', '按钮对象');
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const text = this.getInputValue('text') as string;
    const position = this.getInputValue('position') as { x: number; y: number };
    const size = this.getInputValue('size') as { x: number; y: number };
    const style = this.getInputValue('style') as any;

    try {
      // 获取UI系统
      const uiSystem = context.world?.getSystem('UISystem');
      if (!uiSystem) {
        console.error('UI系统未找到');
        return false;
      }

      // 创建按钮
      const button = (uiSystem as any).createButton({
        text,
        position,
        size,
        style,
        onClick: () => {
          // 触发点击事件
          this.triggerFlow('onClick');
        }
      });

      // 设置输出值
      this.setOutputValue('button', button);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('创建按钮失败:', error);
      return false;
    }
  }
}

/**
 * 创建文本节点
 */
export class CreateTextNode extends FlowNode {
  constructor() {
    super();
    
    // 输入插槽
    this.addInputSlot('trigger', 'flow', '触发');
    this.addInputSlot('text', 'string', '文本内容', '文本');
    this.addInputSlot('position', 'vector2', '位置', { x: 0, y: 0 });
    this.addInputSlot('fontSize', 'number', '字体大小', 16);
    this.addInputSlot('color', 'string', '颜色', '#000000');
    this.addInputSlot('style', 'object', '样式', {});
    
    // 输出插槽
    this.addOutputSlot('success', 'flow', '成功');
    this.addOutputSlot('textElement', 'object', '文本元素');
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const text = this.getInputValue('text') as string;
    const position = this.getInputValue('position') as { x: number; y: number };
    const fontSize = this.getInputValue('fontSize') as number;
    const color = this.getInputValue('color') as string;
    const style = this.getInputValue('style') as any;

    try {
      // 获取UI系统
      const uiSystem = context.world?.getSystem('UISystem');
      if (!uiSystem) {
        console.error('UI系统未找到');
        return false;
      }

      // 创建文本元素
      const textElement = (uiSystem as any).createText({
        text,
        position,
        fontSize,
        color,
        style
      });

      // 设置输出值
      this.setOutputValue('textElement', textElement);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('创建文本失败:', error);
      return false;
    }
  }
}

/**
 * 创建输入框节点
 */
export class CreateInputNode extends FlowNode {
  constructor() {
    super();
    
    // 输入插槽
    this.addInputSlot('trigger', 'flow', '触发');
    this.addInputSlot('placeholder', 'string', '占位符', '请输入...');
    this.addInputSlot('position', 'vector2', '位置', { x: 0, y: 0 });
    this.addInputSlot('size', 'vector2', '大小', { x: 200, y: 30 });
    this.addInputSlot('inputType', 'string', '输入类型', 'text');
    this.addInputSlot('style', 'object', '样式', {});
    
    // 输出插槽
    this.addOutputSlot('success', 'flow', '成功');
    this.addOutputSlot('onChange', 'flow', '值改变');
    this.addOutputSlot('onFocus', 'flow', '获得焦点');
    this.addOutputSlot('onBlur', 'flow', '失去焦点');
    this.addOutputSlot('inputElement', 'object', '输入框元素');
    this.addOutputSlot('value', 'string', '当前值');
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const placeholder = this.getInputValue('placeholder') as string;
    const position = this.getInputValue('position') as { x: number; y: number };
    const size = this.getInputValue('size') as { x: number; y: number };
    const inputType = this.getInputValue('inputType') as string;
    const style = this.getInputValue('style') as any;

    try {
      // 获取UI系统
      const uiSystem = context.world?.getSystem('UISystem');
      if (!uiSystem) {
        console.error('UI系统未找到');
        return false;
      }

      // 创建输入框
      const inputElement = (uiSystem as any).createInput({
        placeholder,
        position,
        size,
        type: inputType,
        style,
        onChange: (value: string) => {
          this.setOutputValue('value', value);
          this.triggerFlow('onChange');
        },
        onFocus: () => {
          this.triggerFlow('onFocus');
        },
        onBlur: () => {
          this.triggerFlow('onBlur');
        }
      });

      // 设置输出值
      this.setOutputValue('inputElement', inputElement);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('创建输入框失败:', error);
      return false;
    }
  }
}

/**
 * 创建滑块节点
 */
export class CreateSliderNode extends FlowNode {
  constructor() {
    super();
    
    // 输入插槽
    this.addInputSlot('trigger', 'flow', '触发');
    this.addInputSlot('min', 'number', '最小值', 0);
    this.addInputSlot('max', 'number', '最大值', 100);
    this.addInputSlot('value', 'number', '初始值', 50);
    this.addInputSlot('step', 'number', '步长', 1);
    this.addInputSlot('position', 'vector2', '位置', { x: 0, y: 0 });
    this.addInputSlot('size', 'vector2', '大小', { x: 200, y: 20 });
    this.addInputSlot('style', 'object', '样式', {});
    
    // 输出插槽
    this.addOutputSlot('success', 'flow', '成功');
    this.addOutputSlot('onChange', 'flow', '值改变');
    this.addOutputSlot('sliderElement', 'object', '滑块元素');
    this.addOutputSlot('currentValue', 'number', '当前值');
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const min = this.getInputValue('min') as number;
    const max = this.getInputValue('max') as number;
    const value = this.getInputValue('value') as number;
    const step = this.getInputValue('step') as number;
    const position = this.getInputValue('position') as { x: number; y: number };
    const size = this.getInputValue('size') as { x: number; y: number };
    const style = this.getInputValue('style') as any;

    try {
      // 获取UI系统
      const uiSystem = context.world?.getSystem('UISystem');
      if (!uiSystem) {
        console.error('UI系统未找到');
        return false;
      }

      // 创建滑块
      const sliderElement = (uiSystem as any).createSlider({
        min,
        max,
        value,
        step,
        position,
        size,
        style,
        onChange: (newValue: number) => {
          this.setOutputValue('currentValue', newValue);
          this.triggerFlow('onChange');
        }
      });

      // 设置输出值
      this.setOutputValue('sliderElement', sliderElement);
      this.setOutputValue('currentValue', value);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('创建滑块失败:', error);
      return false;
    }
  }
}

/**
 * 创建图像节点
 */
export class CreateImageNode extends FlowNode {
  constructor() {
    super();

    // 输入插槽
    this.addInputSlot('trigger', 'flow', '触发');
    this.addInputSlot('src', 'string', '图像源', '');
    this.addInputSlot('position', 'vector2', '位置', { x: 0, y: 0 });
    this.addInputSlot('size', 'vector2', '大小', { x: 100, y: 100 });
    this.addInputSlot('alt', 'string', '替代文本', '');
    this.addInputSlot('style', 'object', '样式', {});

    // 输出插槽
    this.addOutputSlot('success', 'flow', '成功');
    this.addOutputSlot('onLoad', 'flow', '加载完成');
    this.addOutputSlot('onError', 'flow', '加载错误');
    this.addOutputSlot('imageElement', 'object', '图像元素');
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const src = this.getInputValue('src') as string;
    const position = this.getInputValue('position') as { x: number; y: number };
    const size = this.getInputValue('size') as { x: number; y: number };
    const alt = this.getInputValue('alt') as string;
    const style = this.getInputValue('style') as any;

    try {
      // 获取UI系统
      const uiSystem = context.world?.getSystem('UISystem');
      if (!uiSystem) {
        console.error('UI系统未找到');
        return false;
      }

      // 创建图像
      const imageElement = (uiSystem as any).createImage({
        src,
        position,
        size,
        alt,
        style,
        onLoad: () => {
          this.triggerFlow('onLoad');
        },
        onError: () => {
          this.triggerFlow('onError');
        }
      });

      // 设置输出值
      this.setOutputValue('imageElement', imageElement);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('创建图像失败:', error);
      return false;
    }
  }
}

/**
 * 创建面板节点
 */
export class CreatePanelNode extends FlowNode {
  constructor() {
    super();

    // 输入插槽
    this.addInputSlot('trigger', 'flow', '触发');
    this.addInputSlot('position', 'vector2', '位置', { x: 0, y: 0 });
    this.addInputSlot('size', 'vector2', '大小', { x: 300, y: 200 });
    this.addInputSlot('title', 'string', '标题', '面板');
    this.addInputSlot('resizable', 'boolean', '可调整大小', true);
    this.addInputSlot('draggable', 'boolean', '可拖拽', true);
    this.addInputSlot('style', 'object', '样式', {});

    // 输出插槽
    this.addOutputSlot('success', 'flow', '成功');
    this.addOutputSlot('onClose', 'flow', '关闭事件');
    this.addOutputSlot('onResize', 'flow', '调整大小');
    this.addOutputSlot('panelElement', 'object', '面板元素');
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const position = this.getInputValue('position') as { x: number; y: number };
    const size = this.getInputValue('size') as { x: number; y: number };
    const title = this.getInputValue('title') as string;
    const resizable = this.getInputValue('resizable') as boolean;
    const draggable = this.getInputValue('draggable') as boolean;
    const style = this.getInputValue('style') as any;

    try {
      // 获取UI系统
      const uiSystem = context.world?.getSystem('UISystem');
      if (!uiSystem) {
        console.error('UI系统未找到');
        return false;
      }

      // 创建面板
      const panelElement = (uiSystem as any).createPanel({
        position,
        size,
        title,
        resizable,
        draggable,
        style,
        onClose: () => {
          this.triggerFlow('onClose');
        },
        onResize: (newSize: { x: number; y: number }) => {
          this.triggerFlow('onResize');
        }
      });

      // 设置输出值
      this.setOutputValue('panelElement', panelElement);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('创建面板失败:', error);
      return false;
    }
  }
}

/**
 * 设置UI元素属性节点
 */
export class SetUIPropertyNode extends FlowNode {
  constructor() {
    super();

    // 输入插槽
    this.addInputSlot('trigger', 'flow', '触发');
    this.addInputSlot('element', 'object', 'UI元素');
    this.addInputSlot('property', 'string', '属性名', 'visible');
    this.addInputSlot('value', 'any', '属性值', true);

    // 输出插槽
    this.addOutputSlot('success', 'flow', '成功');
    this.addOutputSlot('fail', 'flow', '失败');
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const element = this.getInputValue('element') as any;
    const property = this.getInputValue('property') as string;
    const value = this.getInputValue('value') as any;

    try {
      if (!element) {
        console.error('UI元素为空');
        this.triggerFlow('fail');
        return false;
      }

      // 设置属性
      if (typeof element.setProperty === 'function') {
        element.setProperty(property, value);
      } else {
        element[property] = value;
      }

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('设置UI属性失败:', error);
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 注册UI节点
 */
export function registerUINodes(registry: NodeRegistry): void {
  // 注册创建按钮节点
  registry.registerNodeType({
    type: 'ui/button/create',
    category: NodeCategory.UI,
    constructor: CreateButtonNode,
    label: '创建按钮',
    description: '创建一个可点击的按钮',
    icon: 'button',
    color: '#1890ff',
    tags: ['ui', 'button', 'create', 'interactive']
  });

  // 注册创建文本节点
  registry.registerNodeType({
    type: 'ui/text/create',
    category: NodeCategory.UI,
    constructor: CreateTextNode,
    label: '创建文本',
    description: '创建一个文本显示元素',
    icon: 'text',
    color: '#1890ff',
    tags: ['ui', 'text', 'create', 'display']
  });

  // 注册创建输入框节点
  registry.registerNodeType({
    type: 'ui/input/create',
    category: NodeCategory.UI,
    constructor: CreateInputNode,
    label: '创建输入框',
    description: '创建一个文本输入框',
    icon: 'input',
    color: '#1890ff',
    tags: ['ui', 'input', 'create', 'form']
  });

  // 注册创建滑块节点
  registry.registerNodeType({
    type: 'ui/slider/create',
    category: NodeCategory.UI,
    constructor: CreateSliderNode,
    label: '创建滑块',
    description: '创建一个数值滑块',
    icon: 'slider',
    color: '#1890ff',
    tags: ['ui', 'slider', 'create', 'control']
  });

  // 注册创建图像节点
  registry.registerNodeType({
    type: 'ui/image/create',
    category: NodeCategory.UI,
    constructor: CreateImageNode,
    label: '创建图像',
    description: '创建一个图像显示元素',
    icon: 'image',
    color: '#1890ff',
    tags: ['ui', 'image', 'create', 'media']
  });

  // 注册创建面板节点
  registry.registerNodeType({
    type: 'ui/panel/create',
    category: NodeCategory.UI,
    constructor: CreatePanelNode,
    label: '创建面板',
    description: '创建一个可拖拽的面板窗口',
    icon: 'panel',
    color: '#1890ff',
    tags: ['ui', 'panel', 'create', 'window']
  });

  // 注册设置UI属性节点
  registry.registerNodeType({
    type: 'ui/property/set',
    category: NodeCategory.UI,
    constructor: SetUIPropertyNode,
    label: '设置UI属性',
    description: '设置UI元素的属性值',
    icon: 'property',
    color: '#1890ff',
    tags: ['ui', 'property', 'set', 'modify']
  });
}
